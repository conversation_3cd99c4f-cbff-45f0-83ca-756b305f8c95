-- mpv Lua脚本：进度回报
local http = require 'socket.http'
local ltn12 = require 'ltn12'
local json = require 'dkjson'

local last_path = nil
local last_pos = 0
local report_url = 'http://127.0.0.1:23333/progress'

function send_progress(path, pos)
    local body = json.encode({path = path, position = pos})
    local resp = {}
    http.request{
        url = report_url,
        method = "POST",
        headers = {
            ["Content-Type"] = "application/json"
        },
        source = ltn12.source.string(body),
        sink = ltn12.sink.table(resp)
    }
end

mp.register_event('file-loaded', function()
    last_path = mp.get_property('path')
end)

mp.observe_property('time-pos', 'number', function(_, value)
    if last_path and value then
        if math.abs(value - last_pos) > 5 then -- 每5秒回报一次
            last_pos = value
            send_progress(last_path, value)
        end
    end
end)
