// ==UserScript==
// @name         Jellyfin 本地播放按钮（仅传输ItemId）
// @namespace    http://tampermonkey.net/
// @version      0.2
// @description  在Jellyfin页面添加本地播放按钮，仅通过自定义协议发送ItemId
// <AUTHOR>
// @match        http*://wsq:8096/*
// @grant        none
// ==/UserScript==

(function () {
    'use strict';

    function getItemId() {
        var itemIdMatch = window.location.href.match(/id=([a-zA-Z0-9]+)/);
        return itemIdMatch ? itemIdMatch[1] : '';
    }

    function addLocalPlayButton() {
        var actions = document.querySelector('.mainDetailButtons');
        if (!actions || document.getElementById('local-play-btn')) return;
        var btn = document.createElement('button');
        btn.id = 'local-play-btn';
        btn.innerText = '本地播放';
        btn.style.marginLeft = '10px';
        btn.onclick = function() {
            var itemId = getItemId();
            var url = 'jellyfin-mpv://play?itemid=' + encodeURIComponent(itemId);
            window.location.href = url;
        };
        actions.appendChild(btn);
    }

    // 页面变化时尝试添加按钮
    setInterval(addLocalPlayButton, 2000);
})();
