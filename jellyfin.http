# 用户ID
@userid=3b7f7295a74448d88213ffa1da5f041a

# 项目ID
@itemid=351de55eacf6310f07efdd1c40fb2694

# 访问令牌
@AccessToken=b4e973fa756744f18368bc310f6a15d4

###
# 获取项目详情
# @name getItemDetails
# 
GET http://wsq:8096/Items/{{itemid}}
Authorization: MediaBrowser Token={{AccessToken}}

###
# 报告播放进度
# @name reportProgress
# 
POST http://wsq:8096/Sessions/Playing/Progress
Content-Type: application/json
Authorization: MediaBrowser Token={{AccessToken}}

{
  "ItemId": "{{itemid}}",
  "PositionTicks": 150000000,
  "IsPaused": false
}