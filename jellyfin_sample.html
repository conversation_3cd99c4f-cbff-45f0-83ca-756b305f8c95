<div class="detailPagePrimaryContainer padded-left padded-right detailRibbon">
    <div class="infoWrapper">
        <div class="detailImageContainer padded-left">
            <div class="card backdropCard">
                <div class="cardBox">
                    <div class="cardScalable">
                        <div class="cardPadder cardPadder-backdrop lazy-hidden-children">
                            <span class="cardImageIcon material-icons tv" aria-hidden="true"></span>
                        </div>
                        <canvas aria-hidden="true" width="20" height="20" class="blurhash-canvas lazy-hidden"></canvas>
                        <div class="cardImageContainer coveredImage cardContent lazy blurhashed lazy-image-fadein-fast"
                            style="cursor: default; background-image: url(&quot;http://wsq:8096/Items/bebba511f62dfc0821fa7c7bbdbed3be/Images/Primary?fillHeight=540&amp;fillWidth=960&amp;quality=96&amp;tag=b6352f4ddecff6409010991697f983ad&quot;);">
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="nameContainer">
            <h1 class="parentName focuscontainer-x"><bdi><a style="color:inherit;"
                        class="button-link itemAction emby-button" is="emby-linkbutton" href="#" data-action="link"
                        data-id="930df1c53258083f87acb5ca6b2f070d" data-serverid="3a108c19645a4f19b92bc5c4089d8c9f"
                        data-type="Series" data-isfolder="true">鹰峰同学请穿上衣服</a></bdi></h1>
            <h3 class="itemName infoText subtitle focuscontainer-x"><bdi><a style="color:inherit;"
                        class="button-link itemAction emby-button" is="emby-linkbutton" href="#" data-action="link"
                        data-id="2837319da4b14fd1722ced8011ecda10" data-serverid="3a108c19645a4f19b92bc5c4089d8c9f"
                        data-type="Season" data-isfolder="true">Season 1</a> - 6.
                    Haite.Kudasai.Takamine-san.S01E06</bdi></h3>
        </div>
        <div class="itemMiscInfo itemMiscInfo-primary" style="margin-bottom:.6em">
            <div class="mediaInfoItem">10/05/2025</div>
            <div class="mediaInfoItem">24m</div>
            <div class="endsAt mediaInfoItem">Ends at 0:51</div>
        </div>
        <div class="itemMiscInfo itemMiscInfo-secondary hide" style="margin-bottom:.6em"></div>
    </div>
    <div class="mainDetailButtons focuscontainer-x"> <button is="emby-button" type="button"
            class="button-flat btnPlay detailButton emby-button" title="Play" data-action="resume">
            <div class="detailButton-content"> <span class="material-icons detailButton-icon play_arrow"
                    aria-hidden="true"></span> </div>
        </button> <button is="emby-button" type="button" class="button-flat btnReplay hide detailButton emby-button"
            title="Play" data-action="play">
            <div class="detailButton-content"> <span class="material-icons detailButton-icon replay"
                    aria-hidden="true"></span> </div>
        </button> <button is="emby-button" type="button" class="button-flat btnDownload hide detailButton emby-button"
            title="Download">
            <div class="detailButton-content"> <span class="material-icons detailButton-icon get_app"
                    aria-hidden="true"></span> </div>
        </button> <button is="emby-button" type="button"
            class="button-flat btnPlayTrailer hide detailButton emby-button" title="Trailer">
            <div class="detailButton-content"> <span class="material-icons detailButton-icon theaters"
                    aria-hidden="true"></span> </div>
        </button> <button is="emby-button" type="button" class="button-flat btnInstantMix hide detailButton emby-button"
            title="Instant Mix">
            <div class="detailButton-content"> <span class="material-icons detailButton-icon explore"
                    aria-hidden="true"></span> </div>
        </button> <button is="emby-button" type="button" class="button-flat btnShuffle hide detailButton emby-button"
            title="Shuffle">
            <div class="detailButton-content"> <span class="material-icons detailButton-icon shuffle"
                    aria-hidden="true"></span> </div>
        </button> <button is="emby-button" type="button"
            class="button-flat btnCancelSeriesTimer hide detailButton emby-button" title="Cancel programme">
            <div class="detailButton-content"> <span class="material-icons detailButton-icon delete"
                    aria-hidden="true"></span> </div>
        </button> <button is="emby-button" type="button"
            class="button-flat btnCancelTimer hide detailButton emby-button" title="Stop recording">
            <div class="detailButton-content"> <span class="material-icons detailButton-icon stop"
                    aria-hidden="true"></span> </div>
        </button> <button is="emby-playstatebutton" type="button"
            class="button-flat btnPlaystate detailButton emby-button" title="Mark played"
            data-id="bebba511f62dfc0821fa7c7bbdbed3be" data-serverid="3a108c19645a4f19b92bc5c4089d8c9f"
            data-type="Episode" data-played="false">
            <div class="detailButton-content"> <span
                    class="material-icons detailButton-icon check playstatebutton-icon-unplayed"
                    aria-hidden="true"></span> </div>
        </button> <button is="emby-ratingbutton" type="button"
            class="button-flat btnUserRating detailButton emby-button" title="Add to favourites"
            data-id="bebba511f62dfc0821fa7c7bbdbed3be" data-serverid="3a108c19645a4f19b92bc5c4089d8c9f"
            data-isfavorite="false" data-likes="undefined">
            <div class="detailButton-content"> <span class="material-icons detailButton-icon favorite"
                    aria-hidden="true"></span> </div>
        </button> <button is="emby-button" type="button"
            class="button-flat btnSplitVersions hide detailButton emby-button" title="Split">
            <div class="detailButton-content"> <span class="material-icons detailButton-icon call_split"
                    aria-hidden="true"></span> </div>
        </button> <button is="emby-button" type="button" class="button-flat btnMoreCommands detailButton emby-button"
            title="More">
            <div class="detailButton-content"> <span class="material-icons detailButton-icon more_vert"
                    aria-hidden="true"></span> </div>
        </button> </div>
</div>