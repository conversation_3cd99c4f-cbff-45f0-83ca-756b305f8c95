package main

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"io/ioutil"
	"log"
	"net/http"
	"os/exec"
	"sync"

	"gopkg.in/yaml.v3"
)

// 配置结构体
type Config struct {
	PathPrefixJellyfin string `yaml:"path_prefix_jellyfin"`
	PathPrefixLocal    string `yaml:"path_prefix_local"`
	JellyfinApiUrl     string `yaml:"jellyfin_api_url"`
	JellyfinApiToken   string `yaml:"jellyfin_api_token"`
}

type MediaInfo struct {
	Name   string `json:"name"`
	Path   string `json:"path"`
	ItemId string `json:"itemid"`
}

type ProgressReport struct {
	Path     string  `json:"path"`
	Position float64 `json:"position"`
}

var (
	cache     = make(map[string]MediaInfo) // key: local path
	cacheLock sync.Mutex
	config    Config
)

// 加载配置文件（YAML）
func loadConfig(path string) error {
	data, err := ioutil.ReadFile(path)
	if err != nil {
		return err
	}
	return yaml.Unmarshal(data, &config)
}

func main() {
	// 加载配置
	err := loadConfig("config.yaml")
	if err != nil {
		log.Fatalf("配置文件加载失败: %v", err)
	}

	http.HandleFunc("/play", handlePlay)
	http.HandleFunc("/progress", handleProgress)
	log.Println("服务启动，监听 127.0.0.1:23333 ...")
	log.Fatal(http.ListenAndServe(":23333", nil))
}

// 只接收 itemid，自动查找媒体路径
func handlePlay(w http.ResponseWriter, r *http.Request) {
	if r.Method != "POST" {
		w.WriteHeader(http.StatusMethodNotAllowed)
		return
	}
	var req struct {
		ItemId string `json:"itemid"`
	}
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		w.WriteHeader(http.StatusBadRequest)
		return
	}
	mediaPath, mediaName, err := getMediaPathByItemId(req.ItemId)
	if err != nil {
		log.Println("获取媒体路径失败:", err)
		w.WriteHeader(http.StatusInternalServerError)
		return
	}
	localPath := config.PathPrefixLocal + mediaPath[len(config.PathPrefixJellyfin):]
	info := MediaInfo{Name: mediaName, Path: mediaPath, ItemId: req.ItemId}
	cacheLock.Lock()
	cache[localPath] = info
	cacheLock.Unlock()
	go func() {
		cmd := exec.Command("mpv", localPath)
		_ = cmd.Start()
	}()
	w.WriteHeader(http.StatusOK)
}

// 通过 Jellyfin API 获取媒体路径
func getMediaPathByItemId(itemId string) (string, string, error) {
	url := fmt.Sprintf("%s/Items/%s?api_key=%s", config.JellyfinApiUrl, itemId, config.JellyfinApiToken)
	resp, err := http.Get(url)
	if err != nil {
		return "", "", err
	}
	defer resp.Body.Close()
	if resp.StatusCode != 200 {
		return "", "", fmt.Errorf("Jellyfin API 返回错误: %d", resp.StatusCode)
	}
	var result struct {
		Name         string `json:"Name"`
		MediaSources []struct {
			Path string `json:"Path"`
		} `json:"MediaSources"`
	}
	if err := json.NewDecoder(resp.Body).Decode(&result); err != nil {
		return "", "", err
	}
	if len(result.MediaSources) == 0 {
		return "", "", fmt.Errorf("未找到媒体源")
	}
	return result.MediaSources[0].Path, result.Name, nil
}

func handleProgress(w http.ResponseWriter, r *http.Request) {
	if r.Method != "POST" {
		w.WriteHeader(http.StatusMethodNotAllowed)
		return
	}
	var report ProgressReport
	if err := json.NewDecoder(r.Body).Decode(&report); err != nil {
		w.WriteHeader(http.StatusBadRequest)
		return
	}
	cacheLock.Lock()
	info, ok := cache[report.Path]
	cacheLock.Unlock()
	if !ok {
		w.WriteHeader(http.StatusNotFound)
		return
	}
	// 同步到Jellyfin
	go syncProgressToJellyfin(info.ItemId, report.Position)
	w.WriteHeader(http.StatusOK)
}

func syncProgressToJellyfin(itemId string, position float64) {
	url := fmt.Sprintf("%s/Users/<USER>/PlayingItems/%s/Progress", config.JellyfinApiUrl, itemId)
	body := map[string]interface{}{"PositionTicks": int64(position * 10000000)}
	data, _ := json.Marshal(body)
	req, _ := http.NewRequest("POST", url, io.NopCloser(bytes.NewReader(data)))
	req.Header.Set("X-Emby-Token", config.JellyfinApiToken)
	req.Header.Set("Content-Type", "application/json")
	resp, err := http.DefaultClient.Do(req)
	if err != nil {
		log.Println("Jellyfin进度同步失败:", err)
		return
	}
	defer resp.Body.Close()
	log.Println("进度同步到Jellyfin:", itemId, position)
}
