# jellyfin-mpv 本地播放解决方案

## 项目简介

`jellyfin-mpv` 是一个用于 Jellyfin 媒体服务器的本地播放集成方案，支持在 Linux KDE Plasma 桌面环境下通过自定义协议一键调用本地 mpv 播放器播放 Jellyfin 媒体库中的视频，并自动同步播放进度。

### 功能描述
- 在 Jellyfin 网页端添加“本地播放”按钮
- 通过自定义协议（如 `jellyfin-mpv://play?itemid=xxx`）与本地中转服务通信
- Go 服务端自动查询 Jellyfin API，获取媒体路径并调用 mpv 播放
- mpv Lua 脚本自动回报播放进度到中转服务
- 支持 NFS 挂载的本地媒体库路径映射

## 安装要求与依赖项
- Linux KDE Plasma 桌面环境
- Jellyfin 服务器（建议 10.8+）
- mpv 播放器
- Go 1.18+（用于编译服务端）
- Violentmonkey/Tampermonkey 浏览器扩展（用于用户脚本）
- NFS 挂载的 Jellyfin 媒体库

## 安装步骤

### 1. 克隆项目
```bash
git clone https://github.com/yourname/jellyfin-mpv.git
cd jellyfin-mpv
```

### 2. 配置 Go 服务端
1. 编辑 `config.json`，填写 Jellyfin API 地址、Token、本地挂载路径等：
```json
{
  "path_prefix_jellyfin": "/mnt/nfs/jellyfin/",
  "path_prefix_local": "/mnt/nfs/local/",
  "jellyfin_api_url": "http://localhost:8096",
  "jellyfin_api_token": "YOUR_JELLYFIN_API_TOKEN"
}
```
2. 编译并运行服务端：
```bash
go build -o jellyfin-mpv-server jellyfin-mpv-server.go
./jellyfin-mpv-server
```

### 3. 安装用户脚本
- 在浏览器中安装 Violentmonkey 或 Tampermonkey
- 导入 `jellyfin.user.js` 用户脚本

### 4. 安装 mpv Lua 回报脚本
- 将 `mpv-report.lua` 复制到 mpv 的脚本目录（如 `~/.config/mpv/scripts/`）

## KDE Plasma 注册自定义协议处理程序

### 1. 创建 .desktop 文件
在 `~/.local/share/applications/` 下新建 `jellyfin-mpv.desktop` 文件：
```ini
[Desktop Entry]
Name=Jellyfin MPV Launcher
Exec=curl -X POST "http://127.0.0.1:23333/play" -H "Content-Type: application/json" -d "{\"itemid\":\"%u\"}"
Type=Application
Terminal=false
MimeType=x-scheme-handler/jellyfin-mpv;
```
- `Exec` 路径请替换为你的服务端实际路径

### 2. 注册协议关联
执行以下命令注册协议：
```bash
xdg-mime default jellyfin-mpv.desktop x-scheme-handler/jellyfin-mpv
```

### 3. 验证注册是否成功
- 运行：
```bash
xdg-mime query default x-scheme-handler/jellyfin-mpv
```
- 应输出 `jellyfin-mpv.desktop`
- 在浏览器地址栏输入 `jellyfin-mpv://play?itemid=xxxx`，应自动调用服务端

## 使用说明与示例
1. 在 Jellyfin 网页端进入影片详情页，点击“本地播放”按钮
2. 服务端自动获取 itemid 并查询 Jellyfin API，启动 mpv 播放
3. mpv 播放进度自动同步到 Jellyfin

## 故障排除

### 常见问题
- **按钮未显示**：请确认用户脚本已正确安装，且页面结构与脚本选择器匹配
- **协议未注册/无法调用服务端**：请检查 .desktop 文件和 xdg-mime 配置，确保 Exec 路径正确
- **API Token 错误或无权限**：请在 Jellyfin 后台重新生成 API Token 并更新 config.json
- **NFS 路径映射失败**：请检查挂载路径和配置项，确保本地路径与 Jellyfin 路径一致
- **mpv 无法自动启动**：请确认 mpv 已安装且可在命令行直接调用

如有其他问题，请提交 issue 或查阅 Jellyfin 官方文档。
